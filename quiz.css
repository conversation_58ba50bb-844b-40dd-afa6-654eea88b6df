* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f0f0f0;
  padding: 20px;
}

.container {
  max-width: 600px;
  margin: 0 auto;
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.quiz-container {
  text-align: center;
}

#question {
  font-size: 24px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #333;
}

#timer {
  font-size: 18px;
  color: #e74c3c;
  margin-bottom: 20px;
  font-weight: bold;
}

#options {
  list-style: none;
  margin-bottom: 20px;
}

#options li {
  margin-bottom: 10px;
}

.option {
  width: 100%;
  padding: 15px;
  font-size: 16px;
  border: 2px solid #ddd;
  border-radius: 5px;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: all 0.3s ease;
}

.option:hover {
  background-color: #e8e8e8;
  border-color: #bbb;
}

.option.selected {
  background-color: #3498db;
  color: white;
  border-color: #2980b9;
}

#next-btn, #restart-btn {
  padding: 12px 24px;
  font-size: 16px;
  background-color: #27ae60;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

#next-btn:hover, #restart-btn:hover {
  background-color: #219a52;
}

#result {
  text-align: center;
}

#result h2 {
  color: #333;
  margin-bottom: 20px;
}

#score {
  color: #e74c3c;
  font-weight: bold;
}
