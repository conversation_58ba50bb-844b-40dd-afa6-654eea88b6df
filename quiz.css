* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: neue;
}

body {
  width: 100%;
  height: 100vh;
  font-family: Arial, sans-serif;
  background-color: #f0f0f0;
}

li{
  list-style: none;
  cursor: pointer;
}

.pointer-event {
  pointer-events: none;
}

.unicorn-embed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 0;
}
.main {
  position: absolute;
  top: 15vw;
  left: 40%;
  width: 25vw;
  height: 58vh;
  background: linear-gradient(to bottom right, #ffffff7c 0%, #ffffff9f 20%, rgba(255,255,255,0.25) 100%);
  border-radius: 10px;
  backdrop-filter: blur(15px) saturate(180%);
  -webkit-backdrop-filter: blur(8px) saturate(180%);
}


.wrapper{
  position: absolute;
  top: 1vw;
  left: 0.9vw;
  width: 23vw;
  height: 54vh;
  background: linear-gradient(to bottom right, #ffffff7c 0%, #ffffff9f 20%, rgba(255,255,255,0.25) 100%);
  border-radius: 10px;
  backdrop-filter: blur(15px) saturate(180%);
  -webkit-backdrop-filter: blur(8px) saturate(180%);
}

#next-btn {
  margin-top: 10px;
  padding: 12px 18px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  color: #fff;
  background: linear-gradient(145deg, #111, #444); /* dark gradient */
  box-shadow: 0 4px 10px rgba(0,0,0,0.7), 
              0 -2px 6px rgba(255,255,255,0.2); /* black + very light white */
  transition: all 0.3s ease;
}
#next-btn:hover {
  background: linear-gradient(145deg, #222, #555);
}

ul{
  display: flex;
  flex-direction: column;
  gap: 10px;
}

@font-face {
  font-family: neue;
  src: url(NeueMachina-Regular.otf);
}

@font-face {
  font-family: beba;
  src: url(BebasNeue-Regular.ttf);
}

ul li button{
  padding: 10px 10px;
  border: none;
  background-color: #888;
  border-radius: 3px;
  cursor: pointer;
}