const data = [
  {
    question: "What is the capital of India?",
    answer: "New Delhi",
    options: ["New Delhi", "Mumbai", "Chennai", "Kolkata"],
  },
  {
    question: "What is the capital of USA?",
    answer: "Washington, D.C.",
    options: ["New York", "Los Angeles", "Washington, D.C.", "Chicago"],
  },
  {
    question: "What is the capital of UK?",
    answer: "London",
    options: ["Manchester", "Birmingham", "London", "Liverpool"],
  },
  {
    question: "What is the capital of Australia?",
    answer: "Canberra",
    options: ["Sydney", "Melbourne", "Canberra", "Brisbane"],
  },
];

let questionsDiv = document.querySelector("#question");
let options = document.querySelectorAll(".option");
let timerDiv = document.querySelector("#timer");
let nextQuestion = document.querySelector("#next-btn");

let firstInterval;
let secondInterval;
let score = 0;
let selectedAnswer = null;

let index = 0;
function printQuestionAndOptions() {
  if (index >= data.length) {
    endQuiz();
    return;
  }

  questionsDiv.innerText = data[index].question;
  for (let i = 0; i < options.length; i++) {
    options[i].innerText = data[index].options[i];
    options[i].classList.remove('selected');
  }
  selectedAnswer = null;
}

function endQuiz() {
  clearInterval(firstInterval);
  clearInterval(secondInterval);
  document.querySelector("#result").style.display = "block";
  document.querySelector("#score").innerHTML = score + "/" + data.length;
  document.querySelector(".quiz-container").style.display = "none";
}

function nextQuestionHandler() {
  // Check if an answer was selected and if it's correct
  if (selectedAnswer !== null && selectedAnswer === data[index].answer) {
    score++;
  }

  index++;
  timer = 5;
  timerDiv.innerText = timer;
  printQuestionAndOptions();
}

// Add event listeners for option selection
options.forEach(option => {
  option.addEventListener('click', function() {
    // Remove selected class from all options
    options.forEach(opt => opt.classList.remove('selected'));
    // Add selected class to clicked option
    this.classList.add('selected');
    selectedAnswer = this.innerText;
  });
});

// Add event listener for next button (only once, outside the interval)
nextQuestion.addEventListener('click', nextQuestionHandler);

let timer = 5;
timerDiv.innerText = timer;
printQuestionAndOptions();

firstInterval = setInterval(() => {
  timer--;
  timerDiv.innerText = timer;

  if (timer === 0) {
    nextQuestionHandler();
  }
}, 1000);

// Add restart functionality
document.querySelector("#restart-btn").addEventListener('click', function() {
  // Reset all variables
  index = 0;
  score = 0;
  selectedAnswer = null;
  timer = 5;

  // Clear any existing intervals
  clearInterval(firstInterval);
  clearInterval(secondInterval);

  // Reset display
  document.querySelector("#result").style.display = "none";
  document.querySelector(".quiz-container").style.display = "block";

  // Restart the quiz
  timerDiv.innerText = timer;
  printQuestionAndOptions();

  firstInterval = setInterval(() => {
    timer--;
    timerDiv.innerText = timer;

    if (timer === 0) {
      nextQuestionHandler();
    }
  }, 1000);
});

