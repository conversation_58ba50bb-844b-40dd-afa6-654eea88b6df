const data = [
  {
    question: "What is the capital of India?",
    answer: "New Delhi",
    options: ["New Delhi", "Mumbai", "Chennai", "Kolkata"],
  },
  {
    question: "What is the capital of USA?",
    answer: "Washington, D.C.",
    options: ["New York", "Los Angeles", "Washington, D.C.", "Chicago"],
  },
  {
    question: "What is the capital of UK?",
    answer: "London",
    options: ["Manchester", "Birmingham", "London", "Liverpool"],
  },
  {
    question: "What is the capital of Australia?",
    answer: "Canberra",
    options: ["Sydney", "Melbourne", "Canberra", "Brisbane"],
  },
];

let questionsDiv = document.querySelector("#question");
let options = document.querySelectorAll(".option");
let timerDiv = document.querySelector("#timer");
let nextQuestion = document.querySelector("#next-btn");

let firstInterval;
let secondInterval;

let index = 0;
function printQuestionAndOptions() {
  questionsDiv.innerText = data[index].question;
  for (let i = 0; i < options.length; i++) {
    options[i].innerText = data[index].options[i];
  }
}

let timer = 5;
timerDiv.innerText = timer--;
printQuestionAndOptions();

firstInterval = setInterval(() => {
  if (timer === 0) {
    index++;
    timer = 5;
    timerDiv.innerText = timer--;
    printQuestionAndOptions();
  } else {
    timerDiv.innerText = timer--;
  }

  nextQuestion.addEventListener('click', function(){
    index++;
    printQuestionAndOptions()
  })

  if (index >= data.length) {
    clearInterval(firstInterval);
    clearInterval(secondInterval);
    document.querySelector("#result").style.display = "block";
    document.querySelector("#score").innerHTML = "0/" + data.length;
    document.querySelector(".quiz-container").style.display = "none"
  }
}, 1000);

