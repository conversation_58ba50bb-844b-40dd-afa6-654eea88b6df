const data = [
  {
    question: "What is the capital of India?",
    answer: "New Delhi",
    options: ["New Delhi", "Mumbai", "Chennai", "Kolkata"],
  },
  {
    question: "What is the capital of USA?",
    answer: "Washington, D.C.",
    options: ["New York", "Los Angeles", "Washington, D.C.", "Chicago"],
  },
  {
    question: "What is the capital of UK?",
    answer: "London",
    options: ["Manchester", "Birmingham", "London", "Liverpool"],
  },
  {
    question: "What is the capital of Australia?",
    answer: "Canberra",
    options: ["Sydney", "Melbourne", "Canberra", "Brisbane"],
  },
];

let questionsDiv = document.querySelector("#question");
let options = document.querySelectorAll(".option");
let timerDiv = document.querySelector("#timer");
let nextQuestion = document.querySelector("#next-btn");
let restartBtn = document.querySelector("#restart-btn")

let firstInterval;
let secondInterval;
let userAnswer = [];

let score = 0;
let count = 0;
let isQuestionAnswer = false;

function printQuestionAndOptions() {
  questionsDiv.innerText = data[count].question;
  for (let i = 0; i < options.length; i++) {
    options[i].innerText = data[count].options[i];
    // options[i].classList.remove('selected');
    // options[i].disable = false;
  }
}

let timer = 5;
timerDiv.innerText = timer--;
printQuestionAndOptions();

firstInterval = setInterval(() => {
  if (timer === 0) {
    count++;
    enable();
    timer = 5;
    timerDiv.innerText = timer--;
    printQuestionAndOptions();
  } else timerDiv.innerText = timer--;

  if (count >= data.length) {
    clearInterval(firstInterval);
    clearInterval(secondInterval);
    document.querySelector("#result").style.display = "block";
    document.querySelector("#score").innerHTML = checkUseAnswers() + "/" + data.length;
    document.querySelector(".quiz-container").style.display = "none";
  }

}, 1000);


nextQuestion.onclick = function () {
  count++;
  if (count < data.length) {
    timer = 5;
    timerDiv.innerText = timer--;
    printQuestionAndOptions();
  }
  if (count >= data.length) {
    clearInterval(firstInterval);
    // clearInterval(secondInterval);

    checkUseAnswers();

    document.querySelector("#result").style.display = "block";
    document.querySelector("#score").innerHTML = checkUseAnswers() + "/" + data.length;
    document.querySelector(".quiz-container").style.display = "none";
  }
};


options.forEach((opt, idx) => {
  opt.addEventListener('click', function (e) {
    storeUserAnswer(idx, e);
  });
});

function storeUserAnswer(idx, e) {
  options.forEach(opt => opt.classList.remove('option'));
  e.target.classList.add('selected');
  disable(e);

  userAnswer[count] = data[count].options[idx];
  // console.log(userAnswer[count]);

  isQuestionAnswer = true;
  console.log(userAnswer);
}

function disable() {
  options.forEach((btn) => {

    btn.classList.add("pointer-event")
  });
}

function enable() {
  options.forEach((btn) => {
    // btn.disabled = true;
    btn.classList.remove("pointer-event");
  });
}

function checkUseAnswers() {
  userAnswer.forEach((answer, index) => {
    if (answer === data[index].answer) score++;
  });
  return score;
}


restartBtn.addEventListener("click", function () {
  clearInterval(firstInterval);
  timer = 5;
  score = 0;
  count = 0;
  userAnswer = [];
  isQuestionAnswer = false;

  document.querySelector("#result").style.display = "none";
  document.querySelector(".quiz-container").style.display = "block";
  timerDiv.innerText = timer--;

  printQuestionAndOptions();

  firstInterval = setInterval(() => {
    if (timer === 0) {
      count++;
      timer = 5;
      timerDiv.innerText = timer--;
      printQuestionAndOptions();
    } else timerDiv.innerText = timer--;

    if (count >= data.length) {
      clearInterval(firstInterval);
      clearInterval(secondInterval);
      document.querySelector("#result").style.display = "block";
      document.querySelector("#score").innerHTML = checkUseAnswers() + "/" + data.length;
      document.querySelector(".quiz-container").style.display = "none";
    }

  }, 1000);
});

