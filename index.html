<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Quiz</title>
  <link rel="stylesheet" href="quiz.css" />
  <script src="https://cdn.jsdelivr.net/gh/hiunicornstudio/unicornstudio.js@v1.4.30/dist/unicornStudio.umd.js"></script>
</head>

<style>


  
</style>

<body>
  <div class="unicorn-embed"
    data-us-project="7hkVb1m6qX0BVCAVo09B"
    data-us-scale="1"
    data-us-dpi="1.5"
    data-us-lazyload="true"
    data-us-alttext="Welcome to Unicorn Studio"
    data-us-arialabel="This is a canvas scene">
  </div>

  <div class="main">
    <div class="wrapper">
      <!-- <div>
        <button>start</button>
      </div> -->
      <h1>Quiz Application</h1>
      <div class="container">
        <div class="quiz-container">
          <div id="question">Question text</div>
          <p id="timer"></p>
          <ul id="options">
            <li><button class="option">Option 1</button></li>
            <li><button class="option">Option 2</button></li>
            <li><button class="option">Option 3</button></li>
            <li><button class="option">Option 4</button></li>
          </ul>
          <button id="next-btn">Next Question</button>
        </div>
        <div id="result" style="display: none">
          <h2>Your Score: <span id="score"></span></h2>
          <button id="restart-btn">Restart Quiz</button>
        </div>
      </div>
    </div>
  </div>

  <script src="quiz.js"></script>

  <script>
    // Wait for DOM to be ready before initializing UnicornStudio
    document.addEventListener("DOMContentLoaded", function() {
      if (window.UnicornStudio && typeof UnicornStudio.init === "function") {
        UnicornStudio.init()
          .then((scenes) => {
            // Scenes are ready
          })
          .catch((err) => {
            console.error(err);
          });
      } else {
        console.error("UnicornStudio library not loaded.");
      }
    });
  </script>
</body>

</html>